# 苏拉卡尔塔棋引擎 (Surakarta Chess Engine)

## 项目简介

这是一个基于SAU通用计算机博弈对战平台的苏拉卡尔塔棋引擎实现。该引擎严格遵循比赛规则，支持标准的通信协议，可以与SAU Game Platform进行对战。

## 比赛规则

1. 参赛者掷硬币决定由谁先开始，每次只能移动一个棋子，两人轮流走棋
2. 每个棋子可以向8个方向（上、下、左、右、左上、左下、右上、右下）移动一格（当所去的方向无棋子时）
3. 若要吃掉对方棋子，必须经过至少一个完整的弧线，并且移动路径中不可以有本方棋子阻挡
4. 黑子可以吃掉白子，同样白子沿同一路径的相反方向也可以吃掉黑子
5. 当一方棋子全部被吃掉时棋局结束，有剩余棋子方获胜
6. 当双方都不能再吃掉对方棋子时，剩余棋子多的一方获胜

## 棋盘坐标

- 棋盘从左到右共6列（A-F），从上到下共6行（1-6）
- 第1列第2行的棋子坐标表示为AB
- 初始状态：黑子占据前两行，白子占据后两行

## 文件结构

```plaintext
SurakartaChess-Platform/
├── surakarta_engine.cpp    # 主引擎文件
├── arc_capture.cpp         # 弧线吃子逻辑
├── build.bat               # 编译脚本
├── README.md               # 说明文档
└── 通信协议说明与引擎编写规范.txt
```

## 编译和运行

### 前置要求

- Windows操作系统
- G++编译器（推荐MinGW或TDM-GCC）
- 支持C++11标准

### 编译步骤

1. 确保已安装G++编译器
2. 双击运行 `build.bat` 脚本
3. 编译成功后会生成 `surakarta_engine.exe` 文件

### 使用方法

1. 将生成的 `surakarta_engine.exe` 文件加载到SAU Game Platform中
2. 引擎会自动响应平台的通信协议命令
3. 支持的命令包括：
   - `name?` - 返回引擎名称
   - `new black/white` - 开始新游戏并分配颜色
   - `move XXYY` - 接收对手移动并返回己方移动
   - `error` - 处理错误着法
   - `end` - 游戏结束
   - `quit` - 退出引擎

## 技术特性

- 严格遵循SAU平台通信协议
- 实现了8方向基本移动规则
- 支持弧线吃子逻辑（简化版本）
- 基本的AI决策算法
- 位置评估函数

## 开发说明

引擎采用C++语言开发，主要模块包括：

1. **棋盘管理**：6x6棋盘状态维护
2. **移动验证**：8方向移动和弧线吃子规则检查
3. **AI决策**：基于位置评估的最佳移动生成
4. **通信协议**：与SAU平台的标准通信接口

## 最新修正 (2024)

本项目已经过全面修正，解决了以下关键问题：

1. **坐标系统修正**: 正确实现了列A-F，行A-F的坐标转换
2. **移动验证加强**: 严格验证棋子移动的合法性，防止冲突
3. **弧线吃子完善**: 实现了正确的弧线路径追踪和阻挡检查
4. **AI决策改进**: 优化了评估函数和移动生成算法
5. **程序稳定性**: 修复了输入处理和无限循环问题

### 修正后的文件

- `surakarta_engine_fixed.exe` - 修正后的可执行文件
- `修正总结.md` - 详细的修正说明文档

## 注意事项

- 引擎现在严格遵循苏拉卡尔塔棋规则
- 弧线吃子逻辑已完善，支持正确的路径追踪
- AI算法已优化，提供更好的决策质量
- 引擎名称为"SurakartaEngine"
- 支持标准SAU平台通信协议

## 许可证

本项目仅用于学习和比赛目的。