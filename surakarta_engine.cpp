#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <vector>
#include <algorithm>

using namespace std;

// 棋子类型定义
#define BLACK 0
#define WHITE 1
#define EMPTY 2

// 棋盘大小
#define BOARD_SIZE 6

// 点结构
struct Point {
    int x, y;
    Point() : x(0), y(0) {}
    Point(int x, int y) : x(x), y(y) {}
};

// 步结构
struct Step {
    Point start, end;
    int value;
    Step() : value(0) {}
    Step(Point s, Point e, int v = 0) : start(s), end(e), value(v) {}
};

// 全局变量
int Board[BOARD_SIZE][BOARD_SIZE];  // 棋盘结构
int computerSide;                   // 己方执棋颜色
int gameStarted = 0;               // 对局开始标记

// 函数声明
void initializeBoard();
void printBoard();
int isValidMove(Point start, Point end);
int canCapture(Point start, Point end);
int makeMove(Point start, Point end);
Step generateBestMove();
int evaluatePosition();
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]);
int countPieces(int color);
int isGameOver();

// 初始化棋盘
void initializeBoard() {
    int i, j;

    // 初始化棋盘为空
    for (i = 0; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = EMPTY;
        }
    }

    // 设置初始棋子位置
    // 黑子在上方两行
    for (i = 0; i < 2; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = BLACK;
        }
    }

    // 白子在下方两行
    for (i = 4; i < BOARD_SIZE; i++) {
        for (j = 0; j < BOARD_SIZE; j++) {
            Board[i][j] = WHITE;
        }
    }
}

// 打印棋盘（调试用）
void printBoard() {
    cout << "  A B C D E F" << endl;
    for (int i = 0; i < BOARD_SIZE; i++) {
        cout << (i + 1) << " ";
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == BLACK) {
                cout << "B ";
            } else if (Board[i][j] == WHITE) {
                cout << "W ";
            } else {
                cout << ". ";
            }
        }
        cout << endl;
    }
    cout << endl;
}

// 检查是否为有效的基本移动（8方向，一格）
int isValidMove(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否为空（不能与其他棋子产生冲突）
    if (Board[end.x][end.y] != EMPTY) {
        return 0;
    }

    // 检查是否为8方向移动一格
    int dx = abs(end.x - start.x);
    int dy = abs(end.y - start.y);

    // 8个方向：上下左右和4个对角线方向，每次只能移动一格
    if ((dx == 1 && dy == 0) || (dx == 0 && dy == 1) || (dx == 1 && dy == 1)) {
        return 1;
    }

    return 0;
}

// 弧线吃子判断函数声明
int isValidArcCapture(int startX, int startY, int endX, int endY);

// 吃子判断（使用弧线路径检查）
int canCapture(Point start, Point end) {
    // 检查边界
    if (start.x < 0 || start.x >= BOARD_SIZE || start.y < 0 || start.y >= BOARD_SIZE ||
        end.x < 0 || end.x >= BOARD_SIZE || end.y < 0 || end.y >= BOARD_SIZE) {
        return 0;
    }

    // 检查起始位置是否有己方棋子
    if (Board[start.x][start.y] != computerSide) {
        return 0;
    }

    // 检查目标位置是否有对方棋子
    if (Board[end.x][end.y] != (computerSide ^ 1)) {
        return 0;
    }

    // 使用弧线路径检查
    return isValidArcCapture(start.x, start.y, end.x, end.y);
}

// 执行移动
int makeMove(Point start, Point end) {
    if (isValidMove(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    } else if (canCapture(start, end)) {
        Board[end.x][end.y] = Board[start.x][start.y];
        Board[start.x][start.y] = EMPTY;
        return 1;
    }
    return 0;
}

// 改进的位置评估函数
int evaluatePosition() {
    int myPieces = countPieces(computerSide);
    int opponentPieces = countPieces(computerSide ^ 1);

    int score = 0;

    // 基础评估：己方棋子数 - 对方棋子数
    score += (myPieces - opponentPieces) * 100;

    // 位置评估：中心位置更有价值
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                // 中心位置加分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score += 10;
                }
                // 边缘位置稍微减分
                if (i == 0 || i == BOARD_SIZE-1 || j == 0 || j == BOARD_SIZE-1) {
                    score -= 5;
                }
            } else if (Board[i][j] == (computerSide ^ 1)) {
                // 对方在中心位置减分
                if (i >= 2 && i <= 3 && j >= 2 && j <= 3) {
                    score -= 10;
                }
            }
        }
    }

    return score;
}

// 计算指定颜色的棋子数量
int countPieces(int color) {
    int count = 0;

    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == color) {
                count++;
            }
        }
    }

    return count;
}

// 检查是否有可能的移动
int hasValidMoves(int side) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == side) {
                Point start(i, j);

                // 检查8方向的普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;

                        Point end(i + di, j + dj);
                        if (end.x >= 0 && end.x < BOARD_SIZE &&
                            end.y >= 0 && end.y < BOARD_SIZE &&
                            Board[end.x][end.y] == EMPTY) {
                            return 1;
                        }
                    }
                }

                // 检查弧线吃子
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        if (Board[ti][tj] == (side ^ 1)) {
                            Point end(ti, tj);
                            if (canCapture(start, end)) {
                                return 1;
                            }
                        }
                    }
                }
            }
        }
    }
    return 0;
}

// 检查游戏是否结束
int isGameOver() {
    int blackCount = countPieces(BLACK);
    int whiteCount = countPieces(WHITE);

    // 如果任一方棋子全部被吃掉
    if (blackCount == 0 || whiteCount == 0) {
        return 1;
    }

    // 检查是否还有可能的移动
    if (!hasValidMoves(BLACK) && !hasValidMoves(WHITE)) {
        return 1;
    }

    return 0;
}

// 复制棋盘
void copyBoard(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            dest[i][j] = src[i][j];
        }
    }
}

// 改进的最佳移动生成算法
Step generateBestMove() {
    Step bestMove(Point(-1, -1), Point(-1, -1), -10000);
    vector<Step> captureMoves;
    vector<Step> normalMoves;

    // 遍历所有己方棋子
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (Board[i][j] == computerSide) {
                Point start(i, j);

                // 首先收集所有吃子移动
                for (int ti = 0; ti < BOARD_SIZE; ti++) {
                    for (int tj = 0; tj < BOARD_SIZE; tj++) {
                        Point end(ti, tj);

                        if (canCapture(start, end)) {
                            // 尝试吃子并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition() + 200; // 吃子高奖励

                            Step captureMove(start, end, value);
                            captureMoves.push_back(captureMove);

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }

                // 然后收集普通移动
                for (int di = -1; di <= 1; di++) {
                    for (int dj = -1; dj <= 1; dj++) {
                        if (di == 0 && dj == 0) continue;

                        Point end(i + di, j + dj);

                        if (isValidMove(start, end)) {
                            // 尝试移动并评估
                            int tempBoard[BOARD_SIZE][BOARD_SIZE];
                            copyBoard(Board, tempBoard);

                            makeMove(start, end);
                            int value = evaluatePosition();

                            Step normalMove(start, end, value);
                            normalMoves.push_back(normalMove);

                            // 恢复棋盘
                            copyBoard(tempBoard, Board);
                        }
                    }
                }
            }
        }
    }

    // 优先选择吃子移动
    if (!captureMoves.empty()) {
        for (const Step& move : captureMoves) {
            if (move.value > bestMove.value) {
                bestMove = move;
            }
        }
    } else if (!normalMoves.empty()) {
        // 如果没有吃子移动，选择最佳普通移动
        for (const Step& move : normalMoves) {
            if (move.value > bestMove.value) {
                bestMove = move;
            }
        }
    }

    return bestMove;
}

// 主函数 - 实现SAU平台通信协议
int main() {
    Step step;
    char message[256];

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 程序主循环
    while (1) {
        fflush(stdout);

        // 获取平台消息
        if (scanf("%s", message) != 1) {
            break; // 输入结束或出错时退出
        }

        // 分析命令
        if (strcmp(message, "move") == 0) {
            // 行棋命令
            scanf("%s", message);
            fflush(stdin);

            // 解析对手着法 - 修正坐标系统
            // 第一个字符是列(A-F)，第二个字符是行(A-F)
            step.start.y = message[0] - 'A';  // 列坐标
            step.start.x = message[1] - 'A';  // 行坐标
            step.end.y = message[2] - 'A';    // 列坐标
            step.end.x = message[3] - 'A';    // 行坐标

            // 验证对手移动的合法性（基本检查）
            if (step.start.x >= 0 && step.start.x < BOARD_SIZE &&
                step.start.y >= 0 && step.start.y < BOARD_SIZE &&
                step.end.x >= 0 && step.end.x < BOARD_SIZE &&
                step.end.y >= 0 && step.end.y < BOARD_SIZE &&
                Board[step.start.x][step.start.y] == (computerSide ^ 1)) {

                // 处理对手行棋
                Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
                Board[step.start.x][step.start.y] = EMPTY;
            }

            // 生成己方着法
            step = generateBestMove();

            // 处理己方行棋
            if (step.start.x != -1) {
                Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
                Board[step.start.x][step.start.y] = EMPTY;

                // 输出着法 - 修正坐标系统
                cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
                     << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
            }
        }
        else if (strcmp(message, "new") == 0) {
            // 建立新棋局
            scanf("%s", message);
            fflush(stdin);

            if (strcmp(message, "black") == 0) {
                computerSide = BLACK;
            } else {
                computerSide = WHITE;
            }

            // 初始化棋局
            initializeBoard();
            gameStarted = 1;

            if (computerSide == BLACK) {
                // 黑方先手，生成第一手着法
                step = generateBestMove();

                if (step.start.x != -1) {
                    // 处理己方行棋
                    Board[step.end.x][step.end.y] = Board[step.start.x][step.start.y];
                    Board[step.start.x][step.start.y] = EMPTY;

                    // 输出着法 - 修正坐标系统
                    cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
                         << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
                }
            }
        }
        else if (strcmp(message, "error") == 0) {
            // 着法错误
            fflush(stdin);
            // 重新生成着法
            step = generateBestMove();
            if (step.start.x != -1) {
                cout << "move " << (char)(step.start.y + 'A') << (char)(step.start.x + 'A')
                     << (char)(step.end.y + 'A') << (char)(step.end.x + 'A') << endl;
            }
        }
        else if (strcmp(message, "name?") == 0) {
            // 询问引擎名
            fflush(stdin);
            cout << "name SurakartaEngine" << endl;
        }
        else if (strcmp(message, "end") == 0) {
            // 对局结束
            fflush(stdin);
            gameStarted = 0;
        }
        else if (strcmp(message, "quit") == 0) {
            // 退出引擎
            fflush(stdin);
            cout << "Quit!" << endl;
            break;
        }
    }

    return 0;
}