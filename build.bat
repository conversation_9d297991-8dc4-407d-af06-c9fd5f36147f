@echo off
echo Building Surakarta Chess Engine...

REM 检查是否存在g++编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: G++ compiler not found. Please install MinGW or TDM-GCC.
    echo You can download from: https://www.mingw-w64.org/
    pause
    exit /b 1
)

REM 编译源文件
echo Compiling surakarta_engine.cpp and arc_capture.cpp...
g++ -o surakarta_engine.exe surakarta_engine.cpp arc_capture.cpp -std=c++11 -Wall -O2

REM 检查编译是否成功
if %errorlevel% equ 0 (
    echo Build successful! Generated surakarta_engine.exe
    echo.
    echo You can now use this engine with SAU Game Platform.
) else (
    echo Build failed! Please check the source code for errors.
    pause
    exit /b 1
)

pause