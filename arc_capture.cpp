// 弧线吃子逻辑实现
// 苏拉卡尔塔棋的弧线定义：
// 棋盘四个角落有弧线，棋子必须经过至少一个完整的弧线才能吃子

#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <vector>
#include <algorithm>

using namespace std;

#define BOARD_SIZE 6
#define BLACK 0
#define WHITE 1
#define EMPTY 2

extern int Board[BOARD_SIZE][BOARD_SIZE];
extern int computerSide;

// 弧线路径定义
// 四个角落的弧线路径
struct ArcPoint {
    int x, y;
    ArcPoint() : x(0), y(0) {}
    ArcPoint(int x, int y) : x(x), y(y) {}
};

// 苏拉卡尔塔棋的四个角落弧线
// 左上角弧线路径（顺时针）
vector<ArcPoint> topLeftArc = {
    ArcPoint(0, 0), ArcPoint(0, 1), ArcPoint(1, 1), ArcPoint(1, 0)
};

// 右上角弧线路径（顺时针）
vector<ArcPoint> topRightArc = {
    ArcPoint(0, 5), ArcPoint(0, 4), ArcPoint(1, 4), ArcPoint(1, 5)
};

// 左下角弧线路径（顺时针）
vector<ArcPoint> bottomLeftArc = {
    ArcPoint(5, 0), ArcPoint(4, 0), ArcPoint(4, 1), ArcPoint(5, 1)
};

// 右下角弧线路径（顺时针）
vector<ArcPoint> bottomRightArc = {
    ArcPoint(5, 5), ArcPoint(4, 5), ArcPoint(4, 4), ArcPoint(5, 4)
};

// 检查路径是否可以通过指定的弧线进行吃子
bool pathThroughArc(int startX, int startY, int endX, int endY, const vector<ArcPoint>& arc) {
    // 检查起点是否在弧线上
    bool startOnArc = false;
    int startIndex = -1;
    for (size_t i = 0; i < arc.size(); i++) {
        if (startX == arc[i].x && startY == arc[i].y) {
            startOnArc = true;
            startIndex = i;
            break;
        }
    }

    // 检查终点是否在弧线上
    bool endOnArc = false;
    int endIndex = -1;
    for (size_t i = 0; i < arc.size(); i++) {
        if (endX == arc[i].x && endY == arc[i].y) {
            endOnArc = true;
            endIndex = i;
            break;
        }
    }

    // 如果起点和终点都在弧线上，且不是同一个点，则可能可以吃子
    if (startOnArc && endOnArc && startIndex != endIndex) {
        // 检查弧线路径上是否有阻挡棋子
        int steps = (endIndex - startIndex + arc.size()) % arc.size();
        if (steps == 0) steps = arc.size();

        // 检查路径上的每个点（不包括起点和终点）
        for (int i = 1; i < steps; i++) {
            int checkIndex = (startIndex + i) % arc.size();
            int checkX = arc[checkIndex].x;
            int checkY = arc[checkIndex].y;

            // 如果路径上有己方棋子，则不能吃子
            if (Board[checkX][checkY] == computerSide) {
                return false;
            }
        }
        return true;
    }

    return false;
}



// 检查是否可以通过弧线进行吃子的主函数
bool isValidArcCapture(int startX, int startY, int endX, int endY) {
    // 检查边界
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有己方棋子
    if (Board[startX][startY] != computerSide) {
        return false;
    }

    // 检查目标位置是否有对方棋子
    if (Board[endX][endY] != (computerSide ^ 1)) {
        return false;
    }

    // 检查是否经过任一弧线
    if (pathThroughArc(startX, startY, endX, endY, topLeftArc) ||
        pathThroughArc(startX, startY, endX, endY, topRightArc) ||
        pathThroughArc(startX, startY, endX, endY, bottomLeftArc) ||
        pathThroughArc(startX, startY, endX, endY, bottomRightArc)) {
        return true;
    }

    return false;
}