// 弧线吃子逻辑实现
// 苏拉卡尔塔棋的弧线定义：
// 棋盘四个角落有弧线，棋子必须经过至少一个完整的弧线才能吃子

#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <vector>
#include <algorithm>

using namespace std;

#define BOARD_SIZE 6
#define BLACK 0
#define WHITE 1
#define EMPTY 2

extern int Board[BOARD_SIZE][BOARD_SIZE];
extern int computerSide;

// 弧线路径定义
// 四个角落的弧线路径
struct ArcPoint {
    int x, y;
    ArcPoint() : x(0), y(0) {}
    ArcPoint(int x, int y) : x(x), y(y) {}
};

// 左上角弧线路径
vector<ArcPoint> topLeftArc = {
    ArcPoint(0, 0), ArcPoint(0, 1), ArcPoint(1, 1), ArcPoint(1, 0)
};

// 右上角弧线路径
vector<ArcPoint> topRightArc = {
    ArcPoint(0, 5), ArcPoint(0, 4), ArcPoint(1, 4), ArcPoint(1, 5)
};

// 左下角弧线路径
vector<ArcPoint> bottomLeftArc = {
    ArcPoint(5, 0), ArcPoint(5, 1), ArcPoint(4, 1), ArcPoint(4, 0)
};

// 右下角弧线路径
vector<ArcPoint> bottomRightArc = {
    ArcPoint(5, 5), ArcPoint(5, 4), ArcPoint(4, 4), ArcPoint(4, 5)
};

// 检查路径是否经过指定的弧线
bool pathThroughArc(int startX, int startY, int endX, int endY, const vector<ArcPoint>& arc) {
    // 简化的弧线检查：检查起点和终点是否在弧线的两端
    // 实际实现需要更复杂的路径追踪

    // 检查是否从弧线一端到另一端
    for (size_t i = 0; i < arc.size(); i++) {
        for (size_t j = 0; j < arc.size(); j++) {
            if (i != j) {
                if ((startX == arc[i].x && startY == arc[i].y &&
                     endX == arc[j].x && endY == arc[j].y) ||
                    (startX == arc[j].x && startY == arc[j].y &&
                     endX == arc[i].x && endY == arc[i].y)) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 检查是否可以通过弧线吃子
bool canCaptureViaArc(int startX, int startY, int endX, int endY) {
    // 检查边界
    if (startX < 0 || startX >= BOARD_SIZE || startY < 0 || startY >= BOARD_SIZE ||
        endX < 0 || endX >= BOARD_SIZE || endY < 0 || endY >= BOARD_SIZE) {
        return false;
    }

    // 检查起始位置是否有己方棋子
    if (Board[startX][startY] != computerSide) {
        return false;
    }

    // 检查目标位置是否有对方棋子
    if (Board[endX][endY] != (computerSide ^ 1)) {
        return false;
    }

    // 检查路径中是否有己方棋子阻挡（简化检查）
    // 实际实现需要完整的路径追踪

    // 检查是否经过任一弧线
    if (pathThroughArc(startX, startY, endX, endY, topLeftArc) ||
        pathThroughArc(startX, startY, endX, endY, topRightArc) ||
        pathThroughArc(startX, startY, endX, endY, bottomLeftArc) ||
        pathThroughArc(startX, startY, endX, endY, bottomRightArc)) {
        return true;
    }

    return false;
}

// 更复杂的弧线路径检查（简化版本）
bool isValidArcCapture(int startX, int startY, int endX, int endY) {
    // 这里实现更详细的弧线路径检查
    // 包括检查路径上是否有阻挡棋子等

    // 简化实现：只检查是否在弧线区域内
    bool inArcRegion = false;

    // 检查是否在角落弧线区域
    if ((startX <= 1 && startY <= 1) || (startX <= 1 && startY >= 4) ||
        (startX >= 4 && startY <= 1) || (startX >= 4 && startY >= 4) ||
        (endX <= 1 && endY <= 1) || (endX <= 1 && endY >= 4) ||
        (endX >= 4 && endY <= 1) || (endX >= 4 && endY >= 4)) {
        inArcRegion = true;
    }

    return inArcRegion && canCaptureViaArc(startX, startY, endX, endY);
}